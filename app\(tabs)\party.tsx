import { View, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState, useEffect } from 'react';
import Balloon from '@/components/Balloon';

const { width, height } = Dimensions.get('window');

interface BalloonData {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
}

const partyColors = ['#FF1744', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5', '#2196F3', '#00BCD4', '#4CAF50', '#FFEB3B', '#FF9800'];

export default function PartyPage() {
  const [balloons, setBalloons] = useState<BalloonData[]>([]);

  const generateBalloons = () => {
    const newBalloons: BalloonData[] = [];
    for (let i = 0; i < 12; i++) {
      newBalloons.push({
        id: Math.random().toString(),
        x: Math.random() * (width - 80) + 40,
        y: Math.random() * (height - 180) + 90,
        color: partyColors[Math.floor(Math.random() * partyColors.length)],
        size: Math.random() * 40 + 50,
      });
    }
    setBalloons(newBalloons);
  };

  useEffect(() => {
    generateBalloons();
  }, []);

  const handleBalloonPop = (id: string) => {
    setBalloons(prev => prev.filter(balloon => balloon.id !== id));
    
    // Régénérer deux nouveaux ballons après 800ms
    setTimeout(() => {
      const newBalloons: BalloonData[] = [];
      for (let i = 0; i < 2; i++) {
        newBalloons.push({
          id: Math.random().toString(),
          x: Math.random() * (width - 80) + 40,
          y: Math.random() * (height - 180) + 90,
          color: partyColors[Math.floor(Math.random() * partyColors.length)],
          size: Math.random() * 40 + 50,
        });
      }
      setBalloons(prev => [...prev, ...newBalloons]);
    }, 800);
  };

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={styles.container}
    >
      {balloons.map(balloon => (
        <Balloon
          key={balloon.id}
          id={balloon.id}
          x={balloon.x}
          y={balloon.y}
          color={balloon.color}
          size={balloon.size}
          onPop={handleBalloonPop}
        />
      ))}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});