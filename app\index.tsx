import { View, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState, useEffect } from 'react';
import Balloon from '@/components/Balloon';

const { width, height } = Dimensions.get('window');

interface BalloonData {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
}

const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];

export default function HomePage() {
  const [balloons, setBalloons] = useState<BalloonData[]>([]);

  const generateBalloons = () => {
    const newBalloons: BalloonData[] = [];
    for (let i = 0; i < 8; i++) {
      newBalloons.push({
        id: Math.random().toString(),
        x: Math.random() * (width - 100) + 50,
        y: Math.random() * (height - 200) + 100,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 30 + 60,
      });
    }
    setBalloons(newBalloons);
  };

  useEffect(() => {
    generateBalloons();
  }, []);

  const handleBalloonPop = (id: string) => {
    setBalloons(prev => prev.filter(balloon => balloon.id !== id));
    
    // Régénérer un nouveau ballon après 1 seconde
    setTimeout(() => {
      const newBalloon: BalloonData = {
        id: Math.random().toString(),
        x: Math.random() * (width - 100) + 50,
        y: Math.random() * (height - 200) + 100,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 30 + 60,
      };
      setBalloons(prev => [...prev, newBalloon]);
    }, 1000);
  };

  return (
    <LinearGradient
      colors={['#87CEEB', '#98FB98']}
      style={styles.container}
    >
      {balloons.map(balloon => (
        <Balloon
          key={balloon.id}
          id={balloon.id}
          x={balloon.x}
          y={balloon.y}
          color={balloon.color}
          size={balloon.size}
          onPop={handleBalloonPop}
        />
      ))}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});