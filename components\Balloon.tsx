import { View, StyleSheet, Pressable, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import { useState, useEffect } from 'react';
import ConfettiEffect from './ConfettiEffect';

const { width, height } = Dimensions.get('window');

interface BalloonProps {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
  onPop: (id: string) => void;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export default function Balloon({ id, x, y, color, size, onPop }: BalloonProps) {
  const [showConfetti, setShowConfetti] = useState(false);
  const scale = useSharedValue(1);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);

  useEffect(() => {
    // Animation de flottement
    translateY.value = withRepeat(
      withSequence(
        withTiming(-10, { duration: 2000 }),
        withTiming(10, { duration: 2000 })
      ),
      -1,
      true
    );
  }, []);

  const handlePress = () => {
    // Animation d'éclatement
    scale.value = withTiming(1.3, { duration: 100 }, () => {
      scale.value = withTiming(0, { duration: 200 }, () => {
        runOnJS(setShowConfetti)(true);
        setTimeout(() => {
          runOnJS(onPop)(id);
        }, 1000);
      });
    });
    
    opacity.value = withTiming(0, { duration: 300 });
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value }
    ],
    opacity: opacity.value,
  }));

  return (
    <>
      <AnimatedPressable
        style={[
          styles.balloon,
          {
            left: x,
            top: y,
            width: size,
            height: size * 1.2,
            backgroundColor: color,
          },
          animatedStyle,
        ]}
        onPress={handlePress}
      >
        <View style={[styles.balloonHighlight, { backgroundColor: 'rgba(255,255,255,0.3)' }]} />
        <View style={styles.balloonString} />
      </AnimatedPressable>
      
      {showConfetti && (
        <ConfettiEffect
          x={x + size / 2}
          y={y + size / 2}
          onComplete={() => setShowConfetti(false)}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  balloon: {
    position: 'absolute',
    borderRadius: 100,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  balloonHighlight: {
    position: 'absolute',
    top: '20%',
    left: '25%',
    width: '30%',
    height: '20%',
    borderRadius: 20,
  },
  balloonString: {
    position: 'absolute',
    bottom: -20,
    left: '50%',
    width: 2,
    height: 20,
    backgroundColor: '#8B4513',
    marginLeft: -1,
  },
});