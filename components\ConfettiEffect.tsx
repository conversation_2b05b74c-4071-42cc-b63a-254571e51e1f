import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withD<PERSON>y,
  runOnJS,
} from 'react-native-reanimated';
import { useEffect } from 'react';

interface ConfettiPiece {
  id: number;
  color: string;
  size: number;
  initialX: number;
  initialY: number;
}

interface ConfettiEffectProps {
  x: number;
  y: number;
  onComplete: () => void;
}

const confettiColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FF8A80', '#82B1FF'];

export default function ConfettiEffect({ x, y, onComplete }: ConfettiEffectProps) {
  const pieces: ConfettiPiece[] = [];
  
  // Créer 15 morceaux de confetti
  for (let i = 0; i < 15; i++) {
    pieces.push({
      id: i,
      color: confettiColors[Math.floor(Math.random() * confettiColors.length)],
      size: Math.random() * 8 + 4,
      initialX: x + (Math.random() - 0.5) * 100,
      initialY: y + (Math.random() - 0.5) * 100,
    });
  }

  useEffect(() => {
    // Nettoyer après 2 secondes
    setTimeout(() => {
      onComplete();
    }, 2000);
  }, []);

  return (
    <View style={styles.container}>
      {pieces.map((piece) => (
        <ConfettiPiece
          key={piece.id}
          color={piece.color}
          size={piece.size}
          initialX={piece.initialX}
          initialY={piece.initialY}
        />
      ))}
    </View>
  );
}

function ConfettiPiece({ color, size, initialX, initialY }: Omit<ConfettiPiece, 'id'>) {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);
  const rotation = useSharedValue(0);

  useEffect(() => {
    // Animation d'explosion
    const finalX = (Math.random() - 0.5) * 200;
    const finalY = Math.random() * 150 + 100;

    translateX.value = withTiming(finalX, { duration: 1500 });
    translateY.value = withTiming(finalY, { duration: 1500 });
    rotation.value = withTiming(Math.random() * 720, { duration: 1500 });
    opacity.value = withDelay(500, withTiming(0, { duration: 1000 }));
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotation.value}deg` },
    ],
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.confettiPiece,
        {
          left: initialX,
          top: initialY,
          width: size,
          height: size,
          backgroundColor: color,
        },
        animatedStyle,
      ]}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  confettiPiece: {
    position: 'absolute',
    borderRadius: 2,
  },
});